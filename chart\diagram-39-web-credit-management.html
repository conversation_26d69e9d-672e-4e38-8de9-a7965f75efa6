<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEB网页工具7: 积分管理流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>💎 WEB网页工具7: 积分管理流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over W: 积分余额查询
    W->>A: 查询积分余额(Token)
    A->>A: 验证Token有效性
    A->>R: 查询缓存中的积分余额
    alt 缓存命中
        R->>A: 返回积分余额
        A->>W: 返回积分信息
    else 缓存未命中
        A->>DB: 查询用户积分余额
        A->>R: 更新积分余额缓存
        A->>W: 返回积分信息
    end

    Note over W: 积分明细查询
    W->>A: 查询积分明细(分页参数/Token)
    A->>DB: 查询积分变动记录
    A->>W: 返回分页明细数据

    Note over W: 积分消费验证
    W->>A: 积分消费请求(消费金额/Token)
    A->>DB: 查询当前积分余额
    alt 积分不足
        A->>W: 返回积分不足
    else 积分充足
        A->>DB: 扣减积分余额
        A->>DB: 记录消费明细
        A->>R: 更新积分缓存
        A->>W: 返回消费成功
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>