<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程B-3: 代理推广流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🤝 Py视频创作工具业务流程B-3: 代理推广流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 申请成为代理(Token)
    A->>A: 验证Token和用户资格
    A->>DB: 检查用户状态和历史记录
    alt 用户不符合代理条件
        A->>P: 返回申请失败(原因说明)
    else 用户符合条件
        A->>A: 生成唯一推广码
        A->>DB: 创建代理记录
        A->>R: 缓存代理信息
        A->>P: 返回代理申请成功(推广码)
    end
    
    Note over P: 查询推广数据
    P->>A: 查询推广统计(Token)
    A->>DB: 查询推广用户数量
    A->>DB: 查询推广收益统计
    A->>P: 返回推广数据(用户数/收益/转化率)
    
    Note over P: 新用户通过推广码注册
    P->>A: 用户注册(推广码)
    A->>DB: 验证推广码有效性
    A->>DB: 创建用户并绑定推广关系
    A->>DB: 记录推广奖励
    A->>R: 更新代理统计缓存
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
