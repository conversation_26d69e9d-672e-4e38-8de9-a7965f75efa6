<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * AI生成服务
 */
class AiGenerationService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成文本
     */
    public function generateText(int $userId, string $prompt, ?int $modelId = null, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            if ($modelId) {
                $model = AiModelConfig::active()->find($modelId);
            } else {
                $model = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
            }

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的文本生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '模型服务当前不可用',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateTextGenerationCost($model, $prompt, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'text_generation',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_TEXT_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'max_tokens' => $generationParams['max_tokens'] ?? 1000,
                    'temperature' => $generationParams['temperature'] ?? 0.7,
                    'top_p' => $generationParams['top_p'] ?? 0.9
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeTextGeneration($task);

            Log::info('文本生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'model_id' => $model->id,
                'estimated_cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务已创建',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'model_name' => $model->model_name,
                    'platform' => $model->platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'model_id' => $modelId,
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取任务状态
     */
    public function getTaskStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'input_data' => $task->input_data,
                    'output_data' => $task->output_data,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'error_message' => $task->error_message,
                    'retry_count' => $task->retry_count,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $task->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取任务状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户任务列表
     */
    public function getUserTasks(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = AiGenerationTask::byUser($userId);
            
            // 应用筛选条件
            if (!empty($filters['task_type'])) {
                $query->byType($filters['task_type']);
            }
            
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }
            
            if (!empty($filters['platform'])) {
                $query->byPlatform($filters['platform']);
            }
            
            $tasks = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $tasksData = $tasks->map(function ($task) {
                return [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'tasks' => $tasksData,
                    'pagination' => [
                        'current_page' => $tasks->currentPage(),
                        'total' => $tasks->total(),
                        'per_page' => $tasks->perPage(),
                        'last_page' => $tasks->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户任务列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 重试任务
     */
    public function retryTask(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            if (!$task->canRetry()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务无法重试',
                    'data' => []
                ];
            }

            $task->incrementRetry();

            // 重新执行任务
            if ($task->task_type === AiGenerationTask::TYPE_TEXT_GENERATION) {
                $this->executeTextGeneration($task);
            }

            Log::info('任务重试成功', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'retry_count' => $task->retry_count
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务重试成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'retry_count' => $task->retry_count
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('任务重试失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务重试失败',
                'data' => null
            ];
        }
    }

    /**
     * 执行文本生成（调用AI服务）
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeTextGeneration(AiGenerationTask $task): void
    {
        try {
            $task->start();

            $taskType = 'text_generation';
            $requestData = [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->getInputData('prompt')
                    ]
                ],
                'max_tokens' => $task->getInputData('max_tokens', 1000),
                'temperature' => $task->getInputData('temperature', 0.7),
                'top_p' => $task->getInputData('top_p', 0.9)
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $taskType,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $aiResponse = $response['data'];

                // 解析AI服务响应
                $generatedText = $aiResponse['choices'][0]['message']['content'] ?? '';
                $tokensUsed = $aiResponse['usage']['total_tokens'] ?? 0;

                $outputData = [
                    'text' => $generatedText,
                    'finish_reason' => $aiResponse['choices'][0]['finish_reason'] ?? 'stop',
                    'model' => $aiResponse['model'] ?? $task->model_name,
                    'usage' => $aiResponse['usage'] ?? []
                ];

                $task->complete($outputData, $tokensUsed);

                // 消费积分
                $this->pointsService->consumePoints($task->id);

                Log::info('文本生成任务完成', [
                    'task_id' => $task->id,
                    'platform' => $task->platform,
                    'mode' => $response['mode'],
                    'tokens_used' => $tokensUsed,
                    'processing_time' => $task->processing_time_ms
                ]);
            } else {
                throw new \Exception($response['error'] ?? 'AI服务调用失败');
            }

        } catch (\Exception $e) {
            $task->fail($e->getMessage());

            // 释放积分
            $this->pointsService->releasePoints($task->id);

            Log::error('文本生成任务失败', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 计算文本生成成本
     */
    private function calculateTextGenerationCost(AiModelConfig $model, string $prompt, array $params): float
    {
        $inputTokens = strlen($prompt) / 4; // 简单估算
        $maxTokens = $params['max_tokens'] ?? 1000;
        $totalTokens = $inputTokens + $maxTokens;
        
        return round($totalTokens * $model->cost_per_request, 4);
    }


}
