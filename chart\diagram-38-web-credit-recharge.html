<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEB网页工具6: 充值积分流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>💳 WEB网页工具6: 充值积分流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant TP as 第三方支付平台

    W->>A: 充值请求(套餐ID/支付方式/Token)
    A->>A: 验证Token有效性
    A->>DB: 查询充值套餐信息
    A->>DB: 创建支付订单记录
    A->>TP: 调用第三方支付接口
    TP->>A: 返回支付参数
    A->>W: 返回支付参数(跳转支付页面)

    Note over W: 用户在第三方平台完成支付
    TP->>A: 支付结果回调通知
    A->>TP: 验证回调签名
    A->>DB: 查询订单状态
    alt 订单已处理
        A->>TP: 返回处理成功
    else 订单未处理
        A->>DB: 更新订单状态为已支付
        A->>DB: 增加用户积分余额
        A->>DB: 记录积分变动明细
        A->>TP: 返回处理成功
        
        Note over A: 通知前端更新
        A->>W: WebSocket推送积分更新(如果在线)
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>