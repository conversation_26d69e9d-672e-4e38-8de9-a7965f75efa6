<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\Project;
use App\Models\StyleLibrary;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 项目管理服务
 */
class ProjectService
{
    /**
     * 防刷机制检查
     */
    public function checkAntiSpam(int $userId): array
    {
        try {
            $cacheKey = "project_create_limit_{$userId}";
            $currentCount = Cache::get($cacheKey, 0);
            
            // 每小时最多创建5个项目
            $maxProjectsPerHour = 5;
            
            if ($currentCount >= $maxProjectsPerHour) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '创建项目过于频繁，请稍后再试',
                    'data' => [
                        'current_count' => $currentCount,
                        'max_allowed' => $maxProjectsPerHour,
                        'reset_time' => Carbon::now()->addHour()->format('Y-m-d H:i:s')
                    ]
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '检查通过',
                'data' => []
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
            ];

            Log::error('防刷机制检查失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '防刷机制检查失败',
                'data' => null
            ];
        }
    }

    /**
     * 创建项目（包含剧情）
     */
    public function createWithStory(int $userId, int $styleId, string $storyContent, ?string $title = null, ?string $description = null): array
    {
        try {
            DB::beginTransaction();

            // 验证风格是否存在
            $style = StyleLibrary::active()->find($styleId);
            if (!$style) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '风格不存在',
                    'data' => []
                ];
            }

            // 生成AI标题（模拟）
            $aiGeneratedTitle = $this->generateAITitle($storyContent, $style, $userId);

            // 创建项目
            $project = Project::create([
                'user_id' => $userId,
                'title' => $title ?: $aiGeneratedTitle,
                'description' => $description,
                'style_id' => $styleId,
                'story_content' => $storyContent,
                'ai_generated_title' => $aiGeneratedTitle,
                'title_confirmed' => !empty($title), // 如果用户提供了标题，则认为已确认
                'status' => Project::STATUS_DRAFT,
                'last_accessed_at' => Carbon::now()
            ]);

            // 增加风格使用次数
            $style->incrementUsage();

            // 更新防刷计数器
            $this->updateAntiSpamCounter($userId);

            DB::commit();

            Log::info('项目创建成功', [
                'project_id' => $project->id,
                'user_id' => $userId,
                'style_id' => $styleId,
                'title' => $project->title
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目创建成功',
                'data' => [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'ai_generated_title' => $aiGeneratedTitle,
                    'title_confirmed' => $project->title_confirmed,
                    'status' => $project->status
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'style_id' => $styleId,
                'story_content_length' => strlen($storyContent),
                'title' => $title,
            ];

            Log::error('项目创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 确认项目标题
     */
    public function confirmTitle(int $projectId, int $userId, bool $useAiTitle, ?string $customTitle = null): array
    {
        try {
            DB::beginTransaction();

            $project = Project::byUser($userId)->find($projectId);
            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在',
                    'data' => []
                ];
            }

            if ($project->title_confirmed) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '标题已经确认，无法修改',
                    'data' => []
                ];
            }

            // 确定最终标题
            if ($useAiTitle) {
                $finalTitle = $project->ai_generated_title;
            } else {
                if (empty($customTitle)) {
                    return [
                        'code' => ApiCodeEnum::INVALID_PARAMS,
                        'message' => '自定义标题不能为空',
                        'data' => []
                    ];
                }
                $finalTitle = $customTitle;
            }

            // 更新项目
            $project->title = $finalTitle;
            $project->title_confirmed = true;
            $project->save();

            DB::commit();

            Log::info('项目标题确认成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'use_ai_title' => $useAiTitle,
                'final_title' => $finalTitle
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '标题确认成功',
                'data' => [
                    'title' => $finalTitle,
                    'title_confirmed' => true
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'use_ai_title' => $useAiTitle,
                'custom_title' => $customTitle,
            ];

            Log::error('项目标题确认失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目标题确认失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成AI标题
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function generateAITitle(string $storyContent, StyleLibrary $style, int $userId): string
    {
        try {
            $taskType = 'title_generation';
            $requestData = [
                'model' => 'deepseek-chat',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => "请为以下故事内容生成一个吸引人的标题，风格类型是{$style->category}：\n\n{$storyContent}\n\n要求：标题要简洁有力，符合{$style->category}风格，不超过20个字。"
                    ]
                ],
                'max_tokens' => 100,
                'temperature' => 0.8
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                'deepseek',
                $taskType,
                $requestData,
                $userId
            );

            if ($response['success']) {
                $aiResponse = $response['data'];
                $generatedTitle = $aiResponse['choices'][0]['message']['content'] ?? '';

                // 清理生成的标题（去除引号等）
                $generatedTitle = trim($generatedTitle, '"\'""''');

                if (!empty($generatedTitle)) {
                    return $generatedTitle;
                }
            }
        } catch (\Exception $e) {
            Log::warning('AI标题生成失败，使用默认逻辑', [
                'error' => $e->getMessage(),
                'style_category' => $style->category
            ]);
        }

        // 如果AI生成失败，使用默认逻辑
        $categoryKeywords = [
            'romance' => ['爱情', '浪漫', '心动', '甜蜜'],
            'adventure' => ['冒险', '探索', '征程', '奇遇'],
            'fantasy' => ['魔法', '奇幻', '传说', '神秘'],
            'scifi' => ['未来', '科幻', '星际', '时空'],
            'horror' => ['恐怖', '惊悚', '黑暗', '诡异'],
            'comedy' => ['搞笑', '幽默', '欢乐', '趣味'],
            'drama' => ['戏剧', '人生', '情感', '命运']
        ];

        $keywords = $categoryKeywords[$style->category] ?? ['故事', '传奇', '记录', '回忆'];
        $randomKeyword = $keywords[array_rand($keywords)];

        $titleTemplates = [
            "一个关于{$randomKeyword}的故事",
            "{$randomKeyword}之旅",
            "我的{$randomKeyword}经历",
            "{$randomKeyword}传说",
            "不可思议的{$randomKeyword}"
        ];

        return $titleTemplates[array_rand($titleTemplates)];
    }

    /**
     * 更新防刷计数器
     */
    private function updateAntiSpamCounter(int $userId): void
    {
        $cacheKey = "project_create_limit_{$userId}";
        $currentCount = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $currentCount + 1, 3600); // 1小时过期
    }

    /**
     * 更新项目状态
     */
    public function updateStatus(int $projectId, int $userId, string $status): array
    {
        try {
            $project = Project::byUser($userId)->find($projectId);
            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在',
                    'data' => []
                ];
            }

            $validStatuses = [
                Project::STATUS_DRAFT,
                Project::STATUS_IN_PROGRESS,
                Project::STATUS_COMPLETED,
                Project::STATUS_PUBLISHED,
                Project::STATUS_ARCHIVED
            ];

            if (!in_array($status, $validStatuses)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '无效的项目状态',
                    'data' => []
                ];
            }

            $oldStatus = $project->status;
            $project->status = $status;

            // 根据状态设置相应的时间戳
            switch ($status) {
                case Project::STATUS_COMPLETED:
                    $project->completed_at = Carbon::now();
                    break;
                case Project::STATUS_PUBLISHED:
                    $project->published_at = Carbon::now();
                    $project->is_public = true;
                    break;
            }

            $project->save();

            Log::info('项目状态更新成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'old_status' => $oldStatus,
                'new_status' => $status
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目状态更新成功',
                'data' => [
                    'project_id' => $projectId,
                    'status' => $status,
                    'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'status' => $status,
            ];

            Log::error('项目状态更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目状态更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 创建简单项目
     * 修复500错误 - 添加缺失的createSimpleProject方法
     */
    public function createSimpleProject(int $userId, string $title, string $description = ''): array
    {
        try {
            DB::beginTransaction();

            $project = Project::create([
                'user_id' => $userId,
                'title' => $title,
                'description' => $description,
                'status' => Project::STATUS_DRAFT,
                'title_confirmed' => true,
                'last_accessed_at' => Carbon::now()
            ]);

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目创建成功',
                'data' => [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'status' => $project->status
                ]
            ];

        } catch (\Exception $e) {
            DB::rollback();

            $error_context = [
                'user_id' => $userId,
                'title' => $title,
                'description' => $description,
            ];

            Log::error('简单项目创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '简单项目创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新项目
     * 修复500错误 - 添加缺失的updateProject方法
     */
    public function updateProject(int $projectId, int $userId, array $data): array
    {
        try {
            $project = Project::byUser($userId)->find($projectId);
            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在',
                    'data' => []
                ];
            }

            $updateData = [];
            if (isset($data['title'])) {
                $updateData['title'] = $data['title'];
            }
            if (isset($data['description'])) {
                $updateData['description'] = $data['description'];
            }
            if (isset($data['project_config'])) {
                $updateData['project_config'] = $data['project_config'];
            }

            if (!empty($updateData)) {
                $updateData['updated_at'] = Carbon::now();
                $project->update($updateData);
            }

            Log::info('项目更新成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'updated_fields' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目更新成功',
                'data' => [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'data_keys' => array_keys($data),
            ];

            Log::error('项目更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 删除项目
     * 修复500错误 - 添加缺失的deleteProject方法
     */
    public function deleteProject(int $projectId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $project = Project::byUser($userId)->find($projectId);
            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在',
                    'data' => []
                ];
            }

            // 统计删除的数据
            $deletedResources = $project->resources()->count();
            $deletedCollaborations = $project->collaborators()->count();

            // 删除相关数据
            $project->resources()->delete();
            $project->collaborators()->delete();
            $project->delete();

            DB::commit();

            Log::info('项目删除成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'deleted_resources' => $deletedResources,
                'deleted_collaborations' => $deletedCollaborations
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目删除成功',
                'data' => [
                    'project_id' => $projectId,
                    'deleted_resources' => $deletedResources,
                    'deleted_collaborations' => $deletedCollaborations,
                    'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'project_id' => $projectId,
                'user_id' => $userId,
            ];

            Log::error('项目删除失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目删除失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户项目列表
     */
    public function getUserProjects(int $userId, array $filters = []): array
    {
        try {
            $query = Project::byUser($userId);

            // 应用过滤条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            if (!empty($filters['search'])) {
                $query->where(function ($q) use ($filters) {
                    $q->where('title', 'like', '%' . $filters['search'] . '%')
                      ->orWhere('description', 'like', '%' . $filters['search'] . '%');
                });
            }

            // 分页处理
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $projects = $query->orderBy('updated_at', 'desc')
                             ->paginate($perPage, ['*'], 'page', $page);

            $projectList = [];
            foreach ($projects->items() as $project) {
                $projectList[] = [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'status' => $project->status,
                    'category' => $project->category ?? 'general',
                    'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'projects' => $projectList,
                    'pagination' => [
                        'current_page' => $projects->currentPage(),
                        'per_page' => $projects->perPage(),
                        'total' => $projects->total(),
                        'last_page' => $projects->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
            ];

            Log::error('获取用户项目列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户项目列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取项目详情
     */
    public function getProjectDetail(int $projectId, int $userId): array
    {
        try {
            $project = Project::byUser($userId)->find($projectId);
            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在',
                    'data' => []
                ];
            }

            $data = [
                'project_id' => $project->id,
                'title' => $project->title,
                'description' => $project->description,
                'status' => $project->status,
                'category' => $project->category ?? 'general',
                'style_id' => $project->style_id,
                'story_content' => $project->story_content,
                'ai_generated_title' => $project->ai_generated_title,
                'title_confirmed' => $project->title_confirmed,
                'project_config' => $project->project_config ?? [],
                'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'project_id' => $projectId,
                'user_id' => $userId,
            ];

            Log::error('获取项目详情失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目详情失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取项目列表（公共接口）
     */
    public function getProjectList(array $filters = []): array
    {
        try {
            $query = Project::query();

            // 应用过滤条件
            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['search'])) {
                $query->where(function ($q) use ($filters) {
                    $q->where('title', 'like', '%' . $filters['search'] . '%')
                      ->orWhere('description', 'like', '%' . $filters['search'] . '%');
                });
            }

            // 只显示公开的项目
            $query->where('is_public', true);

            // 分页处理
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $projects = $query->orderBy('updated_at', 'desc')
                             ->paginate($perPage, ['*'], 'page', $page);

            $projectList = [];
            foreach ($projects->items() as $project) {
                $projectList[] = [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'status' => $project->status,
                    'category' => $project->category ?? 'general',
                    'owner_id' => $project->user_id,
                    'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'projects' => $projectList,
                    'pagination' => [
                        'current_page' => $projects->currentPage(),
                        'per_page' => $projects->perPage(),
                        'total' => $projects->total(),
                        'last_page' => $projects->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
            ];

            Log::error('获取项目列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目列表失败',
                'data' => null
            ];
        }
    }
}
