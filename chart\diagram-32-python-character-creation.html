<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具 - 创建角色流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .features {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #4caf50;
        }
        .features h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .features li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🎭 Py视频创作工具 - 创建角色流程</h1>
        
        <div class="description">
            <strong>流程说明：</strong>本图表展示了Py视频创作工具中"创建角色"功能的完整业务流程，包括用户界面交互、AI角色生成、实时进度推送、资源下载等关键环节。严格遵循资源下载架构铁律和环境切换机制。
        </div>

        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant A as 工具API接口服务
    participant C1 as WebSocket服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over U: 🎭 用户进入角色创建页面

    %% 界面初始化阶段
    U->>F: 点击"创建角色"按钮
    F->>A: GET /py-api/auth/verify (Token验证)
    A->>R: 检查Token缓存状态
    A->>DB: 验证用户权限和积分余额
    A->>F: 返回用户信息和权限状态

    alt Token无效或过期
        F->>U: 跳转到登录页面
        Note over F: 用户需要重新登录
    else Token有效
        F->>A: GET /py-api/characters/styles (获取角色风格选项)
        A->>DB: 查询p_character_categories表
        A->>R: 检查风格选项缓存
        DB->>A: 返回角色风格列表(写实风3.0/都市言情3.0/武侠古风3.0等)
        A->>F: 返回风格选项数据
        F->>U: 渲染创建角色对话框<br/>- 形象名输入框(0/30字符)<br/>- 提示词文本区域(0/500字符)<br/>- 风格选择网格<br/>- 生成按钮(显示需要10积分)
    end

    Note over U: 👤 用户交互：填写角色信息

    %% 用户输入阶段
    U->>F: 输入角色名称"可爱小猫女孩"
    F->>F: 实时字符计数显示(8/30)
    
    U->>F: 输入提示词"一个可爱的猫耳少女，粉色长发，大眼睛，穿着白色连衣裙"
    F->>F: 实时字符计数显示(32/500)

    U->>F: 选择角色风格"新动漫国风"
    F->>F: 高亮选中的风格选项

    alt 用户点击AI写作按钮
        U->>F: 点击AI写作辅助
        F->>A: POST /py-api/ai/text-assist<br/>{type: "character_description", context: "可爱小猫女孩"}
        A->>SC: 调用文本生成AI服务
        Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
        SC->>AI: 调用AI文本生成服务
        AI->>SC: 返回优化后的角色描述
        SC->>A: 返回AI生成的描述文本
        A->>F: 返回优化建议
        F->>U: 在提示词区域显示AI建议<br/>用户可选择采用或继续编辑
    end

    Note over U: 🚀 提交角色创建请求

    %% 提交验证阶段
    U->>F: 点击"生成形象"按钮
    F->>F: 前端参数验证<br/>- 角色名称不能为空且≤30字符<br/>- 提示词不能为空且≤500字符<br/>- 必须选择一个风格

    alt 参数验证失败
        F->>U: 显示错误提示信息
        Note over F: 用户需要修正输入后重新提交
    else 参数验证通过
        F->>F: 显示处理罩层和进度条<br/>禁用生成按钮，防止重复提交

        %% 建立WebSocket连接
        F->>C1: 建立WebSocket连接<br/>订阅频道: character_progress_{user_id}
        C1->>F: 连接确认，准备接收进度推送

        %% 提交创建请求
        F->>A: POST /py-api/characters/create<br/>{name: "可爱小猫女孩", description: "...", style_id: 5, token}
        A->>A: 验证请求参数和用户权限
        A->>DB: 检查用户积分余额(p_users表)
        A->>DB: 检查角色名称是否重复(p_character_library表)

        alt 积分不足
            A->>F: 返回错误信息(code: 1006积分不足)
            F->>F: 隐藏处理罩层
            F->>U: 显示积分不足提示，引导充值
        else 角色名称重复
            A->>F: 返回错误信息(code: 1007角色名称已存在)
            F->>F: 隐藏处理罩层
            F->>U: 提示修改角色名称
        else 验证通过
            %% 扣除积分并创建角色记录
            A->>DB: 扣除用户积分(10积分)
            A->>DB: INSERT INTO p_character_library<br/>(user_id, name, description, style_id, status='generating')
            DB->>A: 返回角色ID(character_id)

            %% 进度推送：开始AI生成
            A->>C1: 推送进度更新<br/>{character_id, progress: 10%, status: "初始化AI角色生成"}
            C1->>F: WebSocket实时推送进度
            F->>U: 更新进度条：10% "初始化AI角色生成"

            %% AI角色图像生成阶段
            A->>SC: 调用AiServiceClient进行角色图像生成
            A->>C1: 推送进度<br/>{progress: 25%, status: "分析角色描述"}
            C1->>F: 实时推送进度
            F->>U: 更新进度条：25% "分析角色描述"

            Note over SC: 🚨 环境切换机制
            alt 开发环境 (AI_SERVICE_MODE=mock)
                SC->>AI: 调用虚拟AI平台<br/>模拟角色图像生成
                AI->>SC: 返回模拟生成结果(快速响应)
                A->>C1: 推送进度<br/>{progress: 60%, status: "AI角色图像生成中(模拟)"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：60% "AI角色图像生成中"
            else 生产环境 (AI_SERVICE_MODE=real)
                SC->>AI: 调用真实AI平台<br/>(LiblibAI/MiniMax等)
                A->>C1: 推送进度<br/>{progress: 40%, status: "连接AI平台"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：40% "连接AI平台"
                
                A->>C1: 推送进度<br/>{progress: 70%, status: "AI角色图像生成中"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：70% "AI角色图像生成中"
                
                AI->>SC: 返回真实生成结果
            end

            SC->>A: 返回AI生成结果(含图像URL和元数据)

            alt AI生成失败
                A->>DB: 更新角色状态为失败
                A->>DB: 返还用户积分(10积分)
                A->>C1: 推送进度<br/>{progress: 100%, status: "生成失败，积分已返还"}
                C1->>F: 实时推送进度
                F->>U: 显示生成失败提示
                F->>F: 隐藏处理罩层，恢复界面
            else AI生成成功
                %% 保存角色数据
                A->>C1: 推送进度<br/>{progress: 85%, status: "保存角色数据"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：85% "保存角色数据"

                A->>DB: UPDATE p_character_library SET<br/>image_url=AI返回的图像URL, file_size=文件大小,<br/>generation_params=JSON生成参数, status='completed'
                A->>R: 缓存角色信息(character:{character_id})

                A->>C1: 推送进度<br/>{progress: 95%, status: "准备下载角色图像"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：95% "准备下载角色图像"

                A->>F: 返回角色创建成功<br/>{character_id, name, image_url, download_info}

                %% 资源下载阶段（遵循架构铁律）
                Note over F: 🚨 资源下载架构铁律：Py工具直接从AI平台下载
                F->>AI: 直接从AI平台下载角色图像到本地
                AI->>F: 下载完成
                F->>A: 确认下载完成(local_path)
                A->>DB: 更新下载状态(downloaded_by_python=true)

                A->>C1: 推送进度<br/>{progress: 100%, status: "角色创建完成"}
                C1->>F: 实时推送进度
                F->>U: 更新进度条：100% "角色创建完成"

                %% 界面更新
                F->>C1: 关闭WebSocket连接
                F->>F: 隐藏处理罩层
                F->>F: 显示角色创建成功对话框<br/>- 显示生成的角色图像<br/>- 角色名称和描述<br/>- "保存到角色库"按钮<br/>- "用于创作"按钮
                F->>U: 角色创建完成，可进行后续操作

                alt 用户选择保存到角色库
                    U->>F: 点击"保存到角色库"
                    F->>A: POST /py-api/characters/save-to-library
                    A->>DB: 更新角色为公开状态(可在角色库中使用)
                    A->>F: 返回保存成功
                    F->>U: 提示保存成功
                else 用户选择直接用于创作
                    U->>F: 点击"用于创作"
                    F->>F: 跳转到视频创作页面<br/>自动绑定新创建的角色
                end
            end
        end
    end

    Note over U: ✅ 角色创建流程完成，资源已保存到本地
        </div>

        <div class="features">
            <h3>🎯 流程特性</h3>
            <ul>
                <li><strong>用户体验优化：</strong>实时字符计数、进度条显示、错误提示</li>
                <li><strong>AI写作辅助：</strong>可选的AI文本优化功能</li>
                <li><strong>环境切换机制：</strong>支持开发环境(mock)和生产环境(real)无缝切换</li>
                <li><strong>WebSocket实时通信：</strong>AI生成进度实时推送</li>
                <li><strong>资源下载铁律：</strong>Py工具直接从AI平台下载，服务器不中转</li>
                <li><strong>积分安全机制：</strong>生成失败自动返还积分</li>
                <li><strong>数据库规范：</strong>严格遵循p_character_library表设计</li>
                <li><strong>后续操作：</strong>支持保存到角色库或直接用于创作</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
