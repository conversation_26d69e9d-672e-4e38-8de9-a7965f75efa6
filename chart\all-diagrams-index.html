<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI视频创作工具 - 系统架构图表总览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .diagram-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        .diagram-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .diagram-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        .diagram-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .diagram-card p {
            margin: 0;
            color: #666;
            line-height: 1.6;
        }
        .diagram-card .icon {
            font-size: 2em;
            margin-bottom: 15px;
            display: block;
        }
        .category-title {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0 10px 0;
        }
        .category-title h2 {
            margin: 0;
            font-size: 1.5em;
            font-weight: 400;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0;
            font-size: 2em;
            font-weight: bold;
        }
        .stat-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        .description {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        .description h2 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .description p {
            margin: 0;
            color: #666;
            line-height: 1.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 AI视频创作工具</h1>
            <p>系统架构图表总览</p>
        </div>
        
        <div class="content">
            <div class="description">
                <h2>📋 项目概述</h2>
                <p>本项目是一个完整的AI视频创作工具生态系统，包含Py视频创作工具、WEB网页工具、管理后台、工具API接口服务和AI服务集成模拟返回数据服务五大核心组件。🚨 最新升级：新增AI平台选择功能，支持用户自主选择AI平台和智能推荐系统。以下图表详细展示了系统架构和业务流程设计。</p>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <h3>40</h3>
                    <p>系统架构图表</p>
                </div>
                <div class="stat-card">
                    <h3>5</h3>
                    <p>核心组件</p>
                </div>
                <div class="stat-card">
                    <h3>27</h3>
                    <p>业务流程 🚨</p>
                </div>
                <div class="stat-card">
                    <h3>3</h3>
                    <p>工具平台</p>
                </div>
            </div>

            <div class="diagram-grid">
                <!-- 系统架构总览 -->
                <div class="category-title">
                    <h2>🏗️ 系统架构总览</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-01-system-architecture.html', '_blank')">
                    <span class="icon">🏗️</span>
                    <h3>完整系统架构图</h3>
                    <p>展示整个系统的架构层次和组件关系，包含五大核心组件、环境切换机制和数据流向。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-10-environment-architecture.html', '_blank')">
                    <span class="icon">🌐</span>
                    <h3>环境切换架构图</h3>
                    <p>开发环境和生产环境的对比架构，展示模拟服务和真实服务的切换。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-11-environment-sequence.html', '_blank')">
                    <span class="icon">⏱️</span>
                    <h3>环境切换时序图</h3>
                    <p>环境切换的详细时序流程，展示配置读取和服务调用的完整过程。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-12-simplified-architecture.html', '_blank')">
                    <span class="icon">📋</span>
                    <h3>简化系统架构图</h3>
                    <p>简化版的系统架构概览，突出核心组件和主要连接关系。</p>
                </div>

                <!-- Py视频创作工具 - 用户管理流程 -->
                <div class="category-title">
                    <h2>👤 Py视频创作工具 - 用户管理流程</h2>
                </div>

                <!-- Py视频创作工具用户管理业务流程 -->
                <div class="diagram-card" onclick="window.open('diagram-20-python-user-registration.html', '_blank')">
                    <span class="icon">📝</span>
                    <h3>Py视频创作工具A-1: 用户注册</h3>
                    <p>Py视频创作工具的用户注册流程，包含邮箱验证和账户激活。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-21-python-user-login.html', '_blank')">
                    <span class="icon">🔐</span>
                    <h3>Py视频创作工具A-2: 用户登录</h3>
                    <p>用户登录流程，包含密码验证、自定义 Token生成和状态检查。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-22-python-token-validation.html', '_blank')">
                    <span class="icon">🔑</span>
                    <h3>Py视频创作工具A-3: Token验证</h3>
                    <p>API请求的Token验证流程，包含黑名单检查和用户状态验证。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-23-python-password-change.html', '_blank')">
                    <span class="icon">🔒</span>
                    <h3>Py视频创作工具A-4: 密码修改</h3>
                    <p>用户密码修改流程，包含旧密码验证和Token失效处理。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-24-python-password-reset.html', '_blank')">
                    <span class="icon">🔓</span>
                    <h3>Py视频创作工具A-5: 密码重置</h3>
                    <p>忘记密码重置流程，包含邮件验证和重置Token管理。</p>
                </div>

                <!-- Py视频创作工具 - 业务功能流程 -->
                <div class="category-title">
                    <h2>💼 Py视频创作工具 - 业务功能流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-27-python-credit-recharge.html', '_blank')">
                    <span class="icon">💳</span>
                    <h3>Py视频创作工具B-1: 充值积分</h3>
                    <p>积分充值流程，包含第三方支付集成和支付回调处理。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-28-python-credit-management.html', '_blank')">
                    <span class="icon">💰</span>
                    <h3>Py视频创作工具B-2: 积分管理</h3>
                    <p>积分管理流程，包含余额查询、明细查看和缓存机制。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-29-python-agent-promotion.html', '_blank')">
                    <span class="icon">🤝</span>
                    <h3>Py视频创作工具B-3: 代理推广</h3>
                    <p>代理推广流程，包含代理申请、推广码生成和推广统计。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-30-python-agent-settlement.html', '_blank')">
                    <span class="icon">💸</span>
                    <h3>Py视频创作工具B-4: 代理结算</h3>
                    <p>代理结算流程，包含收益计算、提现申请和转账处理。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-31-python-data-processing.html', '_blank')">
                    <span class="icon">📊</span>
                    <h3>Py视频创作工具B-5: 数据处理</h3>
                    <p>数据处理流程，包含文件上传、数据验证和异步处理。</p>
                </div>

                <!-- Py视频创作工具 - AI核心流程 -->
                <div class="category-title">
                    <h2>🤖 Py视频创作工具 - AI核心流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-25-python-project-creation.html', '_blank')">
                    <span class="icon">🎬</span>
                    <h3>Py视频创作工具C-0: 视频创作项目创建流程</h3>
                    <p>视频创作项目创建流程（纯文本数据处理版），包含Token验证、风格选择、AI文本处理和实时进度推送。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-26-python-ai-task-scheduling.html', '_blank')">
                    <span class="icon">🤖</span>
                    <h3>Py视频创作工具C-1: AI任务调度</h3>
                    <p>AI任务调度流程，支持文生文、图生图、图生视频、语音和音效生成。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-02-business-flow-success.html', '_blank')">
                    <span class="icon">🔄</span>
                    <h3>Py视频创作工具C-2: AI生成成功</h3>
                    <p>AI生成成功的完整业务流程，包含积分扣取、环境切换、结果返回等关键步骤。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-03-business-flow-insufficient-credits.html', '_blank')">
                    <span class="icon">💰</span>
                    <h3>Py视频创作工具C-3: 积分不足</h3>
                    <p>用户积分不足时的处理流程，保护用户资金安全，提供充值建议。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-04-business-flow-failure.html', '_blank')">
                    <span class="icon">❌</span>
                    <h3>Py视频创作工具C-4: AI生成失败</h3>
                    <p>AI生成失败时的处理机制，包含积分退还、错误处理和事件发布。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-05-business-flow-timeout.html', '_blank')">
                    <span class="icon">⏰</span>
                    <h3>Py视频创作工具C-5: 超时处理</h3>
                    <p>超时和中断情况的处理流程，包含超时监控、积分退还和重试建议。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-06-ai-resource-management.html', '_blank')">
                    <span class="icon">📁</span>
                    <h3>Py视频创作工具C-6: 资源管理</h3>
                    <p>AI资源生成与版本管理流程，包含资源创建、版本控制和状态缓存。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-07-resource-download.html', '_blank')">
                    <span class="icon">⬇️</span>
                    <h3>Py视频创作工具C-7: 资源下载</h3>
                    <p>资源下载的核心流程，包含URL获取、直接下载和状态更新。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-08-work-publish.html', '_blank')">
                    <span class="icon">📤</span>
                    <h3>Py视频创作工具C-8: 作品发布</h3>
                    <p>可选的作品发布流程，包含权限检查、审核状态和作品广场发布。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-09-environment-switching.html', '_blank')">
                    <span class="icon">🔀</span>
                    <h3>Py视频创作工具C-9: 环境切换</h3>
                    <p>核心的环境切换机制，支持开发环境mock模式和生产环境real模式。</p>
                </div>

                <!-- Py视频创作工具 - 角色管理流程 -->
                <div class="category-title">
                    <h2>🎭 Py视频创作工具 - 角色管理流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-32-python-character-creation.html', '_blank')">
                    <span class="icon">🎭</span>
                    <h3>Py视频创作工具D-1: 创建角色</h3>
                    <p>角色创建的完整业务流程，包含AI角色生成、角色库上传、AI敏感信息检测和三级审核机制。支持智能审核：AI检测通过自动设为机审状态(所有用户可见)，管理员可进行人工复审，审核不通过时删除服务器图片但保留用户本地图片。</p>
                </div>

                <!-- WEB网页工具 - 用户管理流程 -->
                <div class="category-title">
                    <h2>🌐 WEB网页工具 - 用户管理流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-33-web-user-registration.html', '_blank')">
                    <span class="icon">📝</span>
                    <h3>WEB网页工具1: 用户注册</h3>
                    <p>WEB网页工具的用户注册流程，包含邮箱验证和账户激活。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-34-web-user-login.html', '_blank')">
                    <span class="icon">🔐</span>
                    <h3>WEB网页工具2: 用户登录</h3>
                    <p>用户登录验证流程，包含密码验证和自定义 Token生成。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-35-web-token-validation.html', '_blank')">
                    <span class="icon">🔑</span>
                    <h3>WEB网页工具3: Token验证</h3>
                    <p>API请求Token验证机制，包含黑名单检查和用户状态验证。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-36-web-password-change.html', '_blank')">
                    <span class="icon">🔒</span>
                    <h3>WEB网页工具4: 密码修改</h3>
                    <p>用户密码修改安全流程，包含旧密码验证和Token失效处理。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-37-web-password-reset.html', '_blank')">
                    <span class="icon">🔓</span>
                    <h3>WEB网页工具5: 密码重置</h3>
                    <p>忘记密码重置流程，包含邮件验证和重置Token管理。</p>
                </div>

                <!-- WEB网页工具 - 业务功能流程 -->
                <div class="category-title">
                    <h2>💼 WEB网页工具 - 业务功能流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-38-web-credit-recharge.html', '_blank')">
                    <span class="icon">💳</span>
                    <h3>WEB网页工具6: 充值积分</h3>
                    <p>积分充值和第三方支付流程，支持微信支付和支付宝支付。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-39-web-credit-management.html', '_blank')">
                    <span class="icon">💎</span>
                    <h3>WEB网页工具7: 积分管理</h3>
                    <p>积分查询、消费和明细管理流程，包含缓存优化机制。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-40-web-agent-promotion.html', '_blank')">
                    <span class="icon">🚀</span>
                    <h3>WEB网页工具8: 代理推广</h3>
                    <p>代理申请、推广链接生成和推广统计流程。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-41-web-agent-settlement.html', '_blank')">
                    <span class="icon">💰</span>
                    <h3>WEB网页工具9: 代理结算</h3>
                    <p>代理佣金计算、结算申请和提现处理流程。</p>
                </div>

                <!-- 管理后台业务流程 -->
                <div class="category-title">
                    <h2>🏢 管理后台 - 业务流程</h2>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-16-admin-system-config.html', '_blank')">
                    <span class="icon">🏢</span>
                    <h3>管理后台1: 系统配置管理</h3>
                    <p>管理后台的系统配置管理流程，包含AI平台配置和连通性测试。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-17-admin-user-management.html', '_blank')">
                    <span class="icon">👥</span>
                    <h3>管理后台2: 用户管理</h3>
                    <p>用户管理流程，包含用户查询、账户操作和权限管理。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-18-admin-content-review.html', '_blank')">
                    <span class="icon">✅</span>
                    <h3>管理后台3: 内容审核</h3>
                    <p>内容审核管理流程，包含作品审核和用户通知机制。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-19-admin-data-analytics.html', '_blank')">
                    <span class="icon">📊</span>
                    <h3>管理后台4: 数据统计 🚨 升级</h3>
                    <p>数据统计分析流程，包含缓存机制、报表导出和AI平台使用统计功能。</p>
                </div>

                <div class="diagram-card" onclick="window.open('diagram-20-admin-ai-platform-management.html', '_blank')">
                    <span class="icon">🤖</span>
                    <h3>管理后台5: AI平台管理 🚨 新增</h3>
                    <p>AI平台选择功能管理，包含平台使用统计、用户偏好分析和平台性能监控。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.querySelectorAll('.diagram-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderColor = '#667eea';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.borderColor = '#e9ecef';
            });
        });
    </script>
</body>
</html>
