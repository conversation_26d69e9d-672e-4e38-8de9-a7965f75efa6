<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台业务流程5: AI平台管理流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🤖 管理后台业务流程5: AI平台管理流程 🚨 新增</h1>
        <div class="mermaid">
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant P as AI平台

    Note over M,P: AI平台使用统计查询
    M->>A: 请求AI平台使用统计
    A->>R: 检查统计缓存
    alt 缓存命中
        R->>A: 返回缓存数据
    else 缓存未命中
        A->>DB: 查询PlatformUsageStatistic表
        A->>DB: 聚合平台使用数据
        A->>R: 缓存统计结果(15分钟)
    end
    A->>M: 返回平台使用统计(使用次数/成功率/费用)

    Note over M,P: 用户偏好分析
    M->>A: 请求用户平台偏好分析
    A->>DB: 查询UserModelPreference表
    A->>DB: 分析用户偏好趋势
    A->>A: 生成偏好分析报告
    A->>M: 返回用户偏好分析(热门平台/用户分布)

    Note over M,P: 平台性能监控
    M->>A: 请求平台性能数据
    A->>DB: 查询平台响应时间统计
    A->>DB: 查询平台错误率统计
    A->>DB: 查询平台可用性数据
    A->>A: 计算性能指标
    A->>M: 返回性能监控报告

    Note over M,P: 平台配置管理
    M->>A: 更新平台配置(启用/禁用)
    A->>DB: 更新平台配置表
    A->>R: 清除相关缓存
    A->>M: 返回配置更新结果

    Note over M,P: 平台连通性测试
    M->>A: 请求平台连通性测试
    A->>P: 发送测试请求
    P->>A: 返回测试响应
    A->>DB: 记录测试结果
    A->>M: 返回连通性测试报告
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
