<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * 关注模型
 * 用于管理用户之间的关注关系
 */
class Follow extends Model
{
    use SoftDeletes;

    protected $table = 'follows';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_BLOCKED = 'blocked';
    const STATUS_PENDING = 'pending';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'follower_id',
        'following_id',
        'status',
        'followed_at',
        'metadata'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'followed_at' => 'datetime',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => self::STATUS_ACTIVE
    ];

    /**
     * 关联关注者（发起关注的用户）
     */
    public function follower()
    {
        return $this->belongsTo(User::class, 'follower_id');
    }

    /**
     * 关联被关注者（被关注的用户）
     */
    public function following()
    {
        return $this->belongsTo(User::class, 'following_id');
    }

    /**
     * 作用域：按关注者过滤
     */
    public function scopeByFollower($query, int $followerId)
    {
        return $query->where('follower_id', $followerId);
    }

    /**
     * 作用域：按被关注者过滤
     */
    public function scopeByFollowing($query, int $followingId)
    {
        return $query->where('following_id', $followingId);
    }

    /**
     * 作用域：活跃状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：最近关注
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('followed_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * 检查是否为活跃关注
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 激活关注关系
     */
    public function activate(): void
    {
        $this->status = self::STATUS_ACTIVE;
        $this->followed_at = Carbon::now();
        $this->save();
    }

    /**
     * 阻止关注关系
     */
    public function block(): void
    {
        $this->status = self::STATUS_BLOCKED;
        $this->save();
    }

    /**
     * 获取元数据
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 设置元数据
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }
}