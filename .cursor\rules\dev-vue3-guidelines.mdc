# Vue 3 + Element Plus 开发规范

**文档版本**: v1.0  
**适用项目**: AI视频创作工具系统 - 管理后台 & WEB网页工具  
**技术栈**: Vue 3 + Element Plus + TypeScript + Vite  
**最后更新**: 2025-08-05  

## 📋 目录

- [项目结构规范](#项目结构规范)
- [组件开发规范](#组件开发规范)
- [API接口调用规范](#api接口调用规范)
- [状态管理规范](#状态管理规范)
- [路由配置规范](#路由配置规范)
- [构建部署规范](#构建部署规范)
- [代码质量规范](#代码质量规范)
- [外部文档引用](#外部文档引用)

## 🏗️ 项目结构规范

### 标准目录结构

```
src/
├── api/                    # API接口封装
│   ├── admin.ts           # 管理后台API
│   ├── web.ts             # WEB工具API
│   └── types.ts           # API类型定义
├── components/            # 通用组件
│   ├── common/            # 基础组件
│   ├── business/          # 业务组件
│   └── layout/            # 布局组件
├── views/                 # 页面组件
│   ├── dashboard/         # 仪表板
│   ├── users/             # 用户管理
│   ├── content/           # 内容管理
│   └── system/            # 系统设置
├── router/                # 路由配置
│   ├── index.ts           # 主路由
│   └── modules/           # 路由模块
├── stores/                # 状态管理
│   ├── user.ts            # 用户状态
│   ├── app.ts             # 应用状态
│   └── index.ts           # 状态入口
├── utils/                 # 工具函数
│   ├── request.ts         # HTTP请求封装
│   ├── auth.ts            # 认证工具
│   └── common.ts          # 通用工具
├── styles/                # 样式文件
│   ├── index.scss         # 主样式
│   ├── variables.scss     # 变量定义
│   └── components.scss    # 组件样式
├── types/                 # TypeScript类型
│   ├── api.ts             # API类型
│   ├── common.ts          # 通用类型
│   └── components.ts      # 组件类型
└── App.vue                # 根组件
```

### 文件命名规范

- **组件文件**: PascalCase (UserList.vue)
- **页面文件**: PascalCase (UserManagement.vue)
- **工具文件**: camelCase (userUtils.ts)
- **类型文件**: camelCase (userTypes.ts)
- **样式文件**: kebab-case (user-list.scss)

## 🧩 组件开发规范

### 组件结构模板

```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/components'

// Props定义
interface Props {
  title: string
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// Emits定义
const emit = defineEmits<{
  change: [value: string]
  submit: [data: any]
}>()

// 响应式数据
const loading = ref(false)
const formData = ref({})

// 计算属性
const computedValue = computed(() => {
  return props.data.length
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法定义
const handleSubmit = () => {
  emit('submit', formData.value)
}
</script>

<style scoped lang="scss">
.component-name {
  // 组件样式
}
</style>
```

### 组件命名规范

- **基础组件**: Base前缀 (BaseButton.vue)
- **业务组件**: 业务域前缀 (UserTable.vue)
- **页面组件**: 功能描述 (UserManagement.vue)
- **布局组件**: Layout前缀 (LayoutHeader.vue)

## 🌐 API接口调用规范

### HTTP请求封装

```typescript
// utils/request.ts
import axios from 'axios'
import type { AxiosResponse } from 'axios'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, data, message } = response.data
    if (code === 200) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    ElMessage.error('网络错误')
    return Promise.reject(error)
  }
)

export default request
```

### API接口定义

```typescript
// api/admin.ts
import request from '@/utils/request'
import type { User, UserListParams } from '@/types/api'

export const adminApi = {
  // 用户管理
  getUserList: (params: UserListParams) => 
    request.get<User[]>('/admin/users', { params }),
  
  createUser: (data: Partial<User>) => 
    request.post<User>('/admin/users', data),
  
  updateUser: (id: number, data: Partial<User>) => 
    request.put<User>(`/admin/users/${id}`, data),
  
  deleteUser: (id: number) => 
    request.delete(`/admin/users/${id}`)
}
```

## 🗂️ 状态管理规范

### Pinia Store结构

```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types/api'

export const useUserStore = defineStore('user', () => {
  // State
  const currentUser = ref<User | null>(null)
  const token = ref<string>('')
  
  // Getters
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => currentUser.value?.name || '')
  
  // Actions
  const login = async (credentials: LoginParams) => {
    try {
      const response = await authApi.login(credentials)
      token.value = response.token
      currentUser.value = response.user
      localStorage.setItem('token', response.token)
    } catch (error) {
      throw error
    }
  }
  
  const logout = () => {
    token.value = ''
    currentUser.value = null
    localStorage.removeItem('token')
  }
  
  return {
    currentUser,
    token,
    isLoggedIn,
    userName,
    login,
    logout
  }
})
```

## 🛣️ 路由配置规范

### 路由结构

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layout/MainLayout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'dashboard',
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})

export default router
```

## 🔨 构建部署规范

### Vite配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

### 环境变量配置

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_TITLE=AI视频创作工具管理后台

# .env.production
VITE_API_BASE_URL=https://api.tiptop.cn/api
VITE_APP_TITLE=AI视频创作工具管理后台
```

## 📏 代码质量规范

### ESLint配置

```json
{
  "extends": [
    "@vue/typescript/recommended",
    "@vue/prettier"
  ],
  "rules": {
    "vue/component-name-in-template-casing": ["error", "PascalCase"],
    "vue/component-definition-name-casing": ["error", "PascalCase"],
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn"
  }
}
```

### 代码提交规范

```bash
# 提交信息格式
feat: 新增用户管理功能
fix: 修复登录状态异常
docs: 更新API文档
style: 调整按钮样式
refactor: 重构用户列表组件
test: 添加用户管理测试用例
```

## 📖 外部文档引用

### Context 7调用指南

**需要最新官方文档时，使用Context 7调用以下资源：**

1. **Vue 3官方文档**
   - Composition API详细说明
   - 响应式系统原理
   - 组件通信方式
   - 生命周期钩子

2. **Element Plus组件库**
   - 组件API参考
   - 主题定制方案
   - 国际化配置
   - 最新组件特性

3. **TypeScript文档**
   - 类型定义规范
   - 泛型使用方法
   - 接口设计模式
   - 类型推断技巧

4. **Vite构建工具**
   - 配置选项说明
   - 插件使用方法
   - 优化策略指南
   - 部署最佳实践

### 调用示例

```
当需要了解Vue 3 Composition API时：
→ 使用Context 7调用Vue 3官方文档
→ 获取最新的API说明和示例

当需要Element Plus组件用法时：
→ 使用Context 7调用Element Plus文档
→ 获取组件属性和事件说明
```

---

## 📝 文档维护

- **更新频率**: 每月检查一次，技术栈重大更新时及时更新
- **维护人员**: 前端开发团队
- **版本控制**: 与项目代码同步版本管理
- **反馈机制**: 开发过程中发现问题及时更新文档

**注意**: 本文档专注于项目特定规范，官方技术文档请通过Context 7获取最新版本。
